export interface AstriaPromptParams {
  text: string;
  modelId?: string;
  numImages: string;
  image: File;
  guidanceScale: string;
}

export const createAstriaPackPromptFormData = (
  params: AstriaPromptParams
): FormData => {
  const formData = new FormData();

  let prompt: string = params.text;

  if (params.modelId) {
    prompt = `<lora:${params.modelId}:1.0> ${prompt}`;
  }

  formData.append("prompt[text]", prompt);
  formData.append("prompt[super_resolution]", "true");
  formData.append("prompt[face_correct]", "true");
  formData.append("prompt[num_images]", params.numImages);
  formData.append("denoising_strength", params.guidanceScale);
  formData.append(
    "prompt[callback]",
    "https://optional-callback-url.com/to-your-service-when-ready?prompt_id=1"
  );

  return formData;
};
